import GlobalStyles from '@/constants/GlobalStyles';
import React, {ReactNode} from 'react';
import {StyleSheet, Text, View} from 'react-native';

export type Props = {
  title: string;
  children: ReactNode;
  isLast?: boolean;
};

const SettingsSection: React.FC<Props> = ({title, children, isLast = false}) => {
  return (
    <View style={[styles.section, isLast && styles.lastSection]}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>{children}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    width: '100%',
    marginBottom: 32,
  },
  lastSection: {
    marginBottom: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  sectionContent: {
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    display: 'flex',
    width: '100%',
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default SettingsSection;
