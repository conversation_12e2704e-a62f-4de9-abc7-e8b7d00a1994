import React, {ReactNode} from 'react';
import {StyleSheet, TouchableOpacity, View, Text} from 'react-native';
import GlobalStyles from '@/constants/GlobalStyles';
import {ChevronRightIcon} from 'react-native-heroicons/solid';

export type SettingsItemProps = {
  title: string;
  rightElement?: ReactNode;
  leftIcon?: ReactNode;
  onPress?: () => void;
  disabled?: boolean;
  showDivider?: boolean;
};

const SettingsItem: React.FC<SettingsItemProps> = ({
  title,
  rightElement,
  leftIcon,
  onPress,
  disabled = false,
}) => {
  const Container = onPress ? TouchableOpacity : View;

  return (
    <>
      <Container
        style={styles.row}
        onPress={onPress}
        disabled={disabled}
        {...(onPress && {
          accessibilityRole: 'button',
          accessibilityLabel: title,
          tabIndex: 0,
        })}
      >
        {leftIcon && <View style={styles.leftIconContainer}>{leftIcon}</View>}
        <Text
          style={[styles.titleText, leftIcon ? styles.titleWithIcon : null]}
          numberOfLines={1}
        >
          {title}
        </Text>
        <View style={styles.valueView}>
          {onPress ? (
            <ChevronRightIcon size={24} fill={GlobalStyles.gray.gray700} />
          ) : (
            rightElement
          )}
        </View>
      </Container>
    </>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-between',
    width: '90%',
    marginTop: 20,
    marginBottom: 12,
    alignItems: 'center',
  },
  titleText: {
    flex: 1,
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    lineHeight: 19,
    color: GlobalStyles.base.black,
  },
  titleWithIcon: {
    marginLeft: 12,
  },
  leftIconContainer: {
    marginRight: 4,
  },
  valueView: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
});

export default SettingsItem;
