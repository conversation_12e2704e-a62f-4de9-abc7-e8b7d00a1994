import {useIsFocused} from '@react-navigation/native';
import {isEqual} from 'lodash';
import {memo, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch} from '@/hooks/redux';
import WalletService from '@/services/WalletService';
import {setIsLoggedIn, setUser, setUserAddresses} from '@/storage/actions/authActions';
import {setSeedPhraseVerified} from '@/storage/actions/sharedActions';
import {BodyM, Footer, Subheading, Title} from '@/styles/styled-components';
import theme from '@/styles/themes';
import {AuthAddresses} from '@/types/authTypes';
import {enableNotifications, shuffleArray} from '@/utils';
import {showInfoToast, showWarningToast} from '@/utils/toast';
import MButton from '@components/MButton';
import {getBitcoinWallet, getExistingWallets} from '../helpers/import-wallet-helpers';

const WORD_BOX_WIDTH = theme.isSmallDevice ? 90 : 100;
const WORD_BOX_HEIGHT = theme.isSmallDevice ? 36 : 40;

interface VerifyImportRouteParams {
  shuffledPhrases: string[];
  seedPhrase: string;
}

const VerifyImport = ({route}: {route: {params: VerifyImportRouteParams}}) => {
  const {shuffledPhrases, seedPhrase} = route.params;

  const {t} = useTranslation();
  const viewFocused = useIsFocused();
  const dispatch = useAppDispatch();

  const [selectedBoxes, setSelectedBoxes] = useState<string[]>([]);
  const [shuffledBoxes, setShuffledBoxes] = useState<string[]>([]);

  const [disabled, setDisabled] = useState(true);
  const [loading, setLoading] = useState(false);

  const handleBoxClick = (index: number) => {
    setDisabled(true);
    const clickedWord = selectedBoxes[index];
    if (!clickedWord) return;

    setSelectedBoxes((prev) => prev.filter((_, i) => i !== index));
    setShuffledBoxes((prev) => [...prev, clickedWord]);
  };

  const handleShuffledBoxClick = (index: number) => {
    const clickedWord = shuffledBoxes[index];
    if (!clickedWord) return;

    setShuffledBoxes((prev) => prev.filter((_, i) => i !== index));
    setSelectedBoxes((prev) => {
      const updated = [...prev, clickedWord];
      setDisabled(updated.length !== 12 || !isEqual(updated, seedPhrase.split(' ')));
      return updated;
    });
  };

  const handleImportWallet = async () => {
    setDisabled(true);
    setLoading(true);

    setTimeout(async () => {
      try {
        const walletService = new WalletService();

        const {wallet, address}: any = await getBitcoinWallet(seedPhrase);

        const bitcoinChains = await walletService.walletLoggerBTC(
          address.address,
          wallet.xPubsList && wallet.xPubsList[0] ? wallet.xPubsList[0].accountXpub : '',
        );

        if (bitcoinChains.status === 409) {
          const {wallets, addresses}: any = await getExistingWallets(wallet, address);

          dispatch(setUserAddresses(addresses));
          dispatch(
            setUser({
              wallet: wallets,
              pinCode: '',
            }),
          );

          if (addresses && addresses.length > 0 && addresses[0]?.address) {
            await enableNotifications(addresses[0].address);
          }
          dispatch(setIsLoggedIn());
        } else {
          const createdWallets = await walletService.createWallets(seedPhrase);
          const createdAddresses = await walletService.createAddresses(createdWallets!);

          const {wallets, addresses, loggerRequest} =
            await walletService.createLoggerRequest(
              createdWallets!,
              createdAddresses as AuthAddresses,
            );

          await walletService.walletLogger(loggerRequest);

          dispatch(setSeedPhraseVerified(true));
          dispatch(setUserAddresses(addresses));
          dispatch(setUser({wallet: wallets, pinCode: ''}));

          if (addresses && addresses.length > 0 && addresses[0]?.address) {
            await enableNotifications(addresses[0].address);
          }
          dispatch(setIsLoggedIn());

          setDisabled(false);
          setLoading(false);
        }
      } catch (error) {
        console.error('Wallet restoration error:', error);
        setDisabled(false);
        showWarningToast('There was an error restoring your wallet. Please try again');
      }
    }, 500);
  };

  useEffect(() => {
    const filteredPhrases = shuffledPhrases.filter(Boolean) as string[];
    const shuffledResult = shuffleArray(filteredPhrases) as string[];
    setShuffledBoxes(shuffledResult);
    setSelectedBoxes([]);
    setDisabled(true);
  }, [viewFocused, shuffledPhrases]);

  useEffect(() => {
    if (selectedBoxes.length === 12) {
      if (isEqual(selectedBoxes, seedPhrase.split(' '))) {
        showInfoToast('Seed phrase verified');
      } else {
        setDisabled(true);
        showWarningToast('Please check the order of your seed phrase words');
      }
    }
  }, [selectedBoxes]);

  return (
    <View style={styles.rootContainer}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.headerContainer}>
          <Title style={styles.title}>
            {t('settings.verifySeedPhrase', 'Verify Seed Phrase')}
          </Title>
        </View>

        <Subheading style={styles.subheading}>
          {t(
            'settings.verifySeedPhraseInstruction',
            'Please input your seed phrase in the correct order to verify your wallet.',
          )}
        </Subheading>

        <View style={styles.selectedWordsContainer}>
          {Array(12)
            .fill(null)
            .map((_, index) => {
              const word = selectedBoxes[index];
              return (
                <TouchableOpacity
                  key={index}
                  onPress={() => word && handleBoxClick(index)}
                  style={[
                    styles.wordBox,
                    word ? styles.selectedWordBox : styles.emptyWordBox,
                    index % 3 === 2 ? styles.lastColumnBox : null,
                  ]}
                  disabled={!word}
                >
                  {word ? (
                    <BodyM style={styles.wordText}>{word}</BodyM>
                  ) : (
                    <BodyM style={styles.emptyBoxText}>{index + 1}</BodyM>
                  )}
                </TouchableOpacity>
              );
            })}
        </View>

        <View style={styles.shuffledWordsContainer}>
          {shuffledBoxes.length > 0 &&
            shuffledBoxes.map((word, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => handleShuffledBoxClick(index)}
                style={[styles.wordBox, styles.shuffledWordBox]}
                activeOpacity={0.7}
              >
                <BodyM style={styles.shuffledWordText}>{word}</BodyM>
              </TouchableOpacity>
            ))}
        </View>
      </ScrollView>

      <Footer style={styles.footer}>
        <MButton
          text={t('settings.importWallet', 'Import Wallet')}
          onPress={handleImportWallet}
          disabled={disabled}
          isLoading={loading}
          loadingText="Importing Wallet"
        />
      </Footer>
    </View>
  );
};

const styles = StyleSheet.create({
  rootContainer: {
    flex: 1,
    backgroundColor: theme.colors.base.white,
  },
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.base.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingHorizontal: theme.layout.ph.screen,
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingBottom: theme.spacing.xxxl,
  },
  headerContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  title: {
    marginTop: theme.spacing.md,
    textAlign: 'center',
    fontSize: theme.isSmallDevice ? 20 : 22,
    fontWeight: '600',
  },
  subheading: {
    marginBottom: theme.spacing.xl,
    textAlign: 'center',
    color: theme.colors.neutral[500],
    maxWidth: '90%',
    lineHeight: 22,
    fontSize: theme.isSmallDevice ? 14 : 16,
  },
  selectedWordsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.xl,
  },
  shuffledWordsContainer: {
    width: '100%',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
  },
  wordBox: {
    width: WORD_BOX_WIDTH,
    height: WORD_BOX_HEIGHT,
    marginBottom: theme.spacing.md,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyWordBox: {
    backgroundColor: theme.colors.light.border,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray600,
    borderStyle: 'dashed',
  },
  selectedWordBox: {
    backgroundColor: theme.colors.light.background,
    borderColor: GlobalStyles.gray.gray600,
    borderWidth: 1,
  },
  shuffledWordBox: {
    backgroundColor: theme.colors.light.background,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray600,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    marginBottom: 0,
  },
  lastColumnBox: {
    marginRight: 0,
  },
  wordText: {
    color: GlobalStyles.gray.gray900,
    fontWeight: '500',
    fontSize: theme.isSmallDevice ? 13 : 15,
  },
  shuffledWordText: {
    color: GlobalStyles.gray.gray900,
    fontSize: theme.isSmallDevice ? 13 : 15,
  },
  emptyBoxText: {
    color: GlobalStyles.gray.gray700,
    fontSize: theme.isSmallDevice ? 13 : 15,
  },
  footer: {
    width: '100%',
    paddingHorizontal: theme.layout.ph.screen * 2,
  },
});

export default memo(VerifyImport);
