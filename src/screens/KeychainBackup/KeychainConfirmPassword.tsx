import React, {memo, useCallback, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, Platform, StyleSheet, TextInput, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {KeyboardAvoidingView} from '@/components/KeyboardAvoidingView';
import MButton from '@/components/MButton';
import {PasswordInput} from '@/components/PasswordInput';
import GlobalStyles from '@/constants/GlobalStyles';
import useKeyboard, {useFocusInput} from '@/hooks/keyboard';
import {useAppDispatch, useAppSelector, useAuth} from '@/hooks/redux';
import {setOnboardingSavedToICloud} from '@/storage/actions/sharedActions';
import {BodyM, Caption, Footer} from '@/styles/styled-components';
import theme from '@/styles/themes';
import {showWarningToast} from '@/utils/toast';
import {setKeychainValue} from '@/utils/keychain';
import {encryptMnemonic} from '@/utils/uniqueness';
import {KEYCHAIN_BACKUP_PROVIDER} from './helpers-keychain';

const KeychainConfirmPassword = ({navigation, route}: any) => {
  const {
    walletLabel,
    password: originalPassword,
    fromExistingBackup,
    bottomTabsPresent,
  } = route.params;

  const {t} = useTranslation();
  const inputRef = useRef<TextInput>(null);
  const dispatch = useAppDispatch();
  const {user, userAddresses} = useAuth();
  const {onboardingSavedToICloud} = useAppSelector((state) => state.common);

  useFocusInput(inputRef);
  const {keyboardShown} = useKeyboard();

  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleConfirmPasswordChange = useCallback((text: string) => {
    setConfirmPassword(text);
  }, []);

  const validateConfirmPassword = useCallback((): boolean => {
    if (confirmPassword !== originalPassword) {
      showWarningToast('Passwords do not match');
      return false;
    }
    return true;
  }, [confirmPassword, originalPassword]);

  const handleContinue = useCallback(() => {
    if (onboardingSavedToICloud) {
      dispatch(setOnboardingSavedToICloud(false));
      navigation.replace('ImportNewWallet', {screen: 'ImportNewWallet'} as never);
    } else {
      navigation.replace('BottomTabs', {
        screen: 'Wallet',
        params: {screen: 'WalletHome'},
      });
    }
  }, [dispatch, navigation, onboardingSavedToICloud]);

  const handleConfirmPasswordPress = useCallback(async () => {
    if (!validateConfirmPassword()) {
      return;
    }

    setLoading(true);

    try {
      await setKeychainValue({
        walletLabel,
        value: user.wallet[0]?.mnemonic,
        password: confirmPassword,
        uniqueId: encryptMnemonic(user.wallet[0]?.mnemonic),
      });

      Alert.alert(
        'Backup Successful',
        `Your recovery phrase has been securely backed up to ${KEYCHAIN_BACKUP_PROVIDER}`,
        [
          {
            text: 'Continue',
            onPress: handleContinue,
          },
        ],
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to save backup. Please try again.');
      console.error('Error saving backup:', error);
    } finally {
      setLoading(false);
    }
  }, [
    validateConfirmPassword,
    walletLabel,
    user,
    userAddresses,
    confirmPassword,
    fromExistingBackup,
    handleContinue,
  ]);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? (theme.isSmallDevice ? 60 : 90) : 0}
      style={styles.keyboardAvoidingView}
    >
      <KeyboardAwareScrollView
        style={styles.scrollView}
        keyboardShouldPersistTaps="handled"
        enableAutomaticScroll={false}
        enableOnAndroid
      >
        <View style={styles.content}>
          <Caption style={styles.caption}>{t('keychain.confirmPassword')}</Caption>

          <BodyM style={styles.description}>
            {t('keychain.confirmPasswordDescription')}
          </BodyM>

          <PasswordInput
            ref={inputRef}
            label="Confirm password"
            value={confirmPassword}
            onChangeText={handleConfirmPasswordChange}
            placeholder="Re-enter your password"
          />
        </View>
      </KeyboardAwareScrollView>

      <Footer
        style={[
          styles.footer,
          !bottomTabsPresent && styles.footerWithoutBottomTabs,
          keyboardShown && styles.footerWithoutBottomTabsWithKeyboard,
        ]}
      >
        <MButton
          text="Continue"
          onPress={handleConfirmPasswordPress}
          disabled={!confirmPassword || loading}
          isLoading={loading}
        />
      </Footer>
    </KeyboardAvoidingView>
  );
};

export default memo(KeychainConfirmPassword);

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.screen + 12,
    paddingVertical: theme.layout.pv.screen,
    justifyContent: 'flex-start',
    gap: theme.spacing.xl,
  },
  caption: {
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    paddingHorizontal: theme.spacing.sm,
    color: GlobalStyles.gray.gray900,
  },
  footer: {
    paddingHorizontal: theme.layout.ph.screen * 2,
  },
  footerWithoutBottomTabs: {
    paddingBottom: theme.spacing.xxl,
  },
  footerWithoutBottomTabsWithKeyboard: {
    paddingBottom: -theme.spacing.xxl,
  },
});
