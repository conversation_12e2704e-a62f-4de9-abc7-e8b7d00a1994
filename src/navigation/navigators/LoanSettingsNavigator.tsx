import {LoanSettingsParamList} from '@/navigation/types';
import {createStackNavigator, StackNavigationOptions} from '@react-navigation/stack';
import React, {memo, ReactElement} from 'react';
import {useTranslation} from 'react-i18next';

import GlobalStyles from '@/constants/GlobalStyles';
import {DEFAULT_HEADER_STYLE} from '../utils';

import LoanChangeEmail from '@/screens/Loan/screens/manage-loan/LoanChangeEmail';
import LoanSettings from '@/screens/Loan/screens/manage-loan/LoanSettingsHome';
import LoanChangePassword from '@/screens/Loan/screens/settings/LoanChangePassword';
import TwoFactorAuthScreen from '@/screens/Loan/screens/settings/two-factor-auth/LoanTwoFactorAuth';

const Stack = createStackNavigator<LoanSettingsParamList>();

const screenOptions: StackNavigationOptions = {
  headerShown: true,
  ...DEFAULT_HEADER_STYLE,
  headerStyle: {
    ...DEFAULT_HEADER_STYLE.headerStyle,
    backgroundColor: GlobalStyles.base.white,
  },
};

const LoanSettingsNavigator = (): ReactElement => {
  const {t} = useTranslation();

  return (
    <Stack.Navigator screenOptions={screenOptions} initialRouteName="LoanSettingsHome">
      <Stack.Screen
        name="LoanSettingsHome"
        component={LoanSettings}
        options={{headerTitle: 'Settings'}}
      />
      <Stack.Screen
        name="LoanTwoFactorAuth"
        component={TwoFactorAuthScreen}
        options={{headerTitle: 'Two-Factor Authentication'}}
      />
      <Stack.Screen
        name="LoanChangeEmail"
        component={LoanChangeEmail}
        options={{headerTitle: 'Change Email'}}
      />
      <Stack.Screen
        name="LoanChangePassword"
        component={LoanChangePassword}
        options={{headerTitle: 'Change Password'}}
      />
    </Stack.Navigator>
  );
};

export default memo(LoanSettingsNavigator);
